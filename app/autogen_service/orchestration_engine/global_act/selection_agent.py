import logging

# Import settings directly without relative imports
import os
import sys
from typing import Any, Dict, List, Optional

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_core.model_context import BufferedChatCompletionContext

from ...model_factory import ModelFactory

sys.path.append(os.path.join(os.path.dirname(__file__), "../../.."))
from app.shared.config.base import get_settings

logger = logging.getLogger(__name__)


class SelectionAgent:
    """
    Agent responsible for selecting the best agent from discovered options
    based on task requirements and agent capabilities.
    """

    def __init__(self, model_name: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the Selection Agent.

        Args:
            model_name: The LLM model to use (uses DISCOVERY_LLM_MODEL env var if not provided)
            api_key: API key (uses settings if not provided)
        """
        self.settings = get_settings()
        self.logger = logger
        self.model_name = model_name or os.getenv(
            "DISCOVERY_LLM_MODEL", "gemini-2.5-pro-preview-06-05"
        )
        self.api_key = api_key or self.settings.requesty.api_key
        self.llm_type = os.getenv("DISCOVERY_LLM_TYPE", "openai")
        self.agent = None

    async def initialize(self) -> None:
        """Initialize the agent with model client and context."""
        try:
            self.logger.info("Initializing Selection Agent...")

            # Create model client using ModelFactory
            model_config = {
                "llm_type": self.llm_type,
                "provider": (
                    "GoogleChatCompletionClient"
                    if self.llm_type == "google"
                    else "OpenAIChatCompletionClient"
                ),
                "model": self.model_name,
                "api_key": self.api_key,
                "base_url": self.settings.requesty.base_url,
            }

            chat_completion_client = ModelFactory.create_model_client(model_config)
            if not chat_completion_client:
                raise ValueError(f"Failed to create {self.llm_type} model client")

            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=32)

            # Define system message for selection
            system_message = """You are a Selection Agent specialized in choosing the best agent from a list of discovered candidates.

Your task is to:
1. Receive discovered agents from the Discovery Agent with their scores and reasons
2. Analyze the candidates based on their relevance to the original task
3. Select the single best agent for the task OR reject all agents if none are suitable
4. Provide confidence score and reasoning for your selection

IMPORTANT SELECTION CRITERIA:
- The agent's specialization and expertise must be directly relevant to the task domain
- If the highest scoring agent is still not a good match for the task, respond with "NONE"
- Be strict about domain relevance - don't select unrelated agents just because they have the highest score

Examples of when to reject all agents:
- Cooking/food tasks when only technical/research agents are available
- Creative tasks when only analytical agents are available  
- Physical world tasks when only digital/software agents are available

Choose wisely based on agent scores, specialization match, and task requirements. It's better to reject all agents than to select an inappropriate one."""

            # Create the assistant agent
            self.agent = AssistantAgent(
                name="selection_agent",
                description="Selects the best agent from discovered candidates",
                model_client=chat_completion_client,
                system_message=system_message,
                model_context=model_context,
                tools=[],
                reflect_on_tool_use=False,
            )

            self.logger.info("Selection Agent initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize Selection Agent: {e}")
            raise e

    async def select_agent(self, discovery_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Select the best agent from discovery results.

        Args:
            discovery_results: Results from DiscoveryAgent containing discovered agents

        Returns:
            Dict containing selection results
        """
        if not self.agent:
            raise ValueError(
                "Selection Agent not initialized. Call initialize() first."
            )

        try:
            discovered_agents = discovery_results.get("discovered_agents", [])
            task_analysis = discovery_results.get("task_analysis", {})

            self.logger.info(
                f"Selecting from {len(discovered_agents)} discovered agents"
            )

            if not discovered_agents:
                return {
                    "selected_agent": None,
                    "confidence": 0.0,
                    "reason": "No agents were discovered to select from",
                    "alternative_options": [],
                    "risk_assessment": "No suitable agents available",
                    "recommendation": "Task cannot be completed with available agents",
                    "discovery_results": discovery_results,
                    "raw_selection": "",
                }

            # Create selection prompt
            agents_summary = "\n".join(
                [
                    f"Agent {i+1}: {agent['agent_name']} (ID: {agent['agent_id']})\n"
                    f"  - Score: {agent['score']}\n"
                    f"  - Specialization: {agent['specialization']}\n"
                    f"  - Tools: {', '.join(agent['tools'])}\n"
                    f"  - Workflows: {', '.join(agent['workflows'])}\n"
                    f"  - Expertise: {', '.join(agent['expertise'])}\n"
                    f"  - Discovery Reason: {agent['reason']}\n"
                    for i, agent in enumerate(discovered_agents)
                ]
            )

            selection_prompt = f"""
            Please select the best agent for this task from the discovered options:
            
            TASK REQUIREMENTS:
            - Task Summary: {task_analysis.get('task_summary', 'No summary')}
            - Domain: {task_analysis.get('domain', 'unknown')}
            - Requirements: {', '.join(task_analysis.get('requirements', []))}
            - Constraints: {', '.join(task_analysis.get('constraints', []))}
            - Technologies: {', '.join(task_analysis.get('technologies', []))}
            - Output Type: {task_analysis.get('output_type', 'unknown')}
            - Urgency: {task_analysis.get('urgency', 'medium')}
            - Complexity: {task_analysis.get('complexity', 'moderate')}
            
            DISCOVERED AGENTS:
            {agents_summary}
            
            DISCOVERY SUMMARY: {discovery_results.get('summary', 'No summary')}
            
            SELECTION CRITERIA:
            1. MINIMUM SCORE THRESHOLD: Only consider agents with score >= 0.75
            2. DOMAIN RELEVANCE: Agent specialization must match the task domain
            3. REJECT IF UNSUITABLE: If no agent meets criteria, respond with "NONE"
            
            Examples of mismatches to REJECT:
            - Research/Technical agents for cooking/food tasks
            - Software agents for physical world tasks
            - Analytical agents for creative/artistic tasks
            
            Please respond in exactly this format:
            
            SELECTED_AGENT: [agent_id or NONE]
            CONFIDENCE: [0.0-1.0]
            PRIMARY_REASON: [detailed justification for selection or rejection]
            ALTERNATIVE_OPTIONS: [agent_id_2, agent_id_3 or NONE]
            RISK_ASSESSMENT: [potential risks or limitations]
            RECOMMENDATION: [final recommendation including "no suitable agent available" if rejecting]
            
            If no agent has score >= 0.75 AND relevant specialization, respond with SELECTED_AGENT: NONE
            """

            text_message = TextMessage(content=selection_prompt, source="user")
            response = await self.agent.on_messages(
                [text_message], cancellation_token=None
            )

            # Extract response content
            if hasattr(response, "chat_message") and hasattr(
                response.chat_message, "content"
            ):
                selection_result = response.chat_message.content
            elif hasattr(response, "content"):
                selection_result = response.content
            else:
                selection_result = str(response)

            # Parse the selection results
            parsed_results = self._parse_selection_result(
                selection_result, discovered_agents
            )
            parsed_results["discovery_results"] = discovery_results
            parsed_results["raw_selection"] = selection_result

            selected_agent_id = parsed_results.get("selected_agent_id")
            self.logger.info(
                f"Selection completed: {selected_agent_id or 'No agent selected'}"
            )
            return parsed_results

        except Exception as e:
            self.logger.error(f"Error in agent selection: {e}")
            return {
                "selected_agent": None,
                "selected_agent_id": None,
                "confidence": 0.0,
                "reason": f"Error during selection: {str(e)}",
                "alternative_options": [],
                "risk_assessment": f"Selection failed due to error: {str(e)}",
                "recommendation": "Unable to complete selection due to technical error",
                "discovery_results": discovery_results,
                "error": str(e),
                "raw_selection": "",
            }

    def _parse_selection_result(
        self, selection_result: str, discovered_agents: List[Dict]
    ) -> Dict[str, Any]:
        """Parse the structured selection result into a dictionary."""
        result = {
            "selected_agent": None,
            "selected_agent_id": None,
            "confidence": 0.0,
            "reason": "",
            "alternative_options": [],
            "risk_assessment": "",
            "recommendation": "",
        }

        try:
            lines = selection_result.split("\n")
            for line in lines:
                line = line.strip()
                if ":" in line:
                    key, value = line.split(":", 1)
                    key = key.strip().lower()
                    value = value.strip()

                    if key == "selected_agent":
                        if (
                            value.upper() == "NONE"
                            or value == "[agent_id or NONE]"
                            or value.strip() == ""
                        ):
                            # No agent selected - this is valid when no suitable agent found
                            result["selected_agent"] = None
                            result["selected_agent_id"] = None
                        elif value != "[agent_id]":
                            # Find the selected agent details
                            selected_agent_details = self._find_agent_by_id(
                                value, discovered_agents
                            )
                            if selected_agent_details:
                                result["selected_agent"] = selected_agent_details
                                result["selected_agent_id"] = value
                            else:
                                # Agent ID not found in discovered agents
                                result["selected_agent"] = None
                                result["selected_agent_id"] = None
                    elif key == "confidence":
                        try:
                            # Extract numeric value from string like "0.95" or "[0.0-1.0]"
                            import re

                            confidence_match = re.search(r"(\d+\.?\d*)", value)
                            if confidence_match:
                                result["confidence"] = float(confidence_match.group(1))
                        except ValueError:
                            result["confidence"] = 0.0
                    elif key == "primary_reason":
                        result["reason"] = value
                    elif key == "alternative_options":
                        result["alternative_options"] = [
                            opt.strip() for opt in value.split(",") if opt.strip()
                        ]
                    elif key == "risk_assessment":
                        result["risk_assessment"] = value
                    elif key == "recommendation":
                        result["recommendation"] = value

        except Exception as e:
            self.logger.error(f"Error parsing selection result: {e}")

        return result

    def _find_agent_by_id(
        self, agent_id: str, discovered_agents: List[Dict]
    ) -> Optional[Dict[str, Any]]:
        """Find agent details by ID in the discovered agents list."""
        for agent in discovered_agents:
            if agent["agent_id"] == agent_id:
                return agent
        return None

    def get_agent(self) -> AssistantAgent:
        """Get the underlying AssistantAgent instance for group chat."""
        if not self.agent:
            raise ValueError("Agent not initialized")
        return self.agent

    def is_initialized(self) -> bool:
        """Check if the agent is initialized."""
        return self.agent is not None

    @classmethod
    async def create_and_initialize(
        cls,
        model_name: Optional[str] = None,
        api_key: Optional[str] = None,
    ) -> "SelectionAgent":
        """
        Convenience method to create and initialize an agent in one call.
        """
        agent = cls(model_name=model_name, api_key=api_key)
        await agent.initialize()
        return agent
